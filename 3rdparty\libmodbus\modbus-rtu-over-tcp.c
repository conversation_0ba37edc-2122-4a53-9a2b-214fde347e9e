#include <stdio.h>
#include <errno.h>
#include <assert.h>
#include "modbus-private.h"
#include "modbus-rtu-private.h"
#include "modbus-tcp-private.h"
#include "modbus-rtu-over-tcp.h"

extern int _modbus_tcp_prepare_response_tid(const uint8_t *req, int *req_length);
extern int _modbus_rtu_send_msg_pre(uint8_t *req, int req_length);
extern ssize_t _modbus_tcp_send(modbus_t *ctx, const uint8_t *req, int req_length);
extern int _modbus_tcp_receive(modbus_t *ctx, uint8_t *req);
extern ssize_t _modbus_tcp_recv(modbus_t *ctx, uint8_t *rsp, int rsp_length);
extern int _modbus_tcp_check_integrity(modbus_t *ctx, uint8_t *msg, const int msg_length);
extern int _modbus_rtu_pre_check_confirmation(modbus_t *ctx, const uint8_t *req,
                                              const uint8_t *rsp, int rsp_length);
extern int _modbus_tcp_connect(modbus_t *ctx);
extern void _modbus_tcp_close(modbus_t *ctx);
extern int _modbus_tcp_flush(modbus_t *ctx);
extern int _modbus_tcp_select(modbus_t *ctx, fd_set *rset, struct timeval *tv, int length_to_read);
extern void _modbus_tcp_free(modbus_t *ctx);

static int _modbus_set_slave(modbus_t *ctx, int slave)
{
    /* Broadcast address is 0 (MODBUS_BROADCAST_ADDRESS) */
    if (slave >= 0 && slave <= 247) {
        ctx->slave = slave;
    } else if (slave == MODBUS_TCP_SLAVE) {
        /* The special value MODBUS_TCP_SLAVE (0xFF) can be used in TCP mode to
         * restore the default value. */
        ctx->slave = slave;
    } else {
        errno = EINVAL;
        return -1;
    }

    return 0;
}

#ifdef PDSTARS

static int _modbus_file_write_request_basis(modbus_t *ctx, int function, modbus_file_io fileInfo[], int infoCount, uint8_t *req)
{

    int currentCount = 3;
    int i = 0, j = 0;

    assert(ctx->slave != -1);
    req[0] = ctx->slave;
    req[1] = function;
    for(; i < infoCount; i++)
    {
        if(currentCount >= MODBUS_RTU_MAX_ADU_LENGTH)
        {
            return -1;
        }
        req[currentCount ++] = 6;

        if(currentCount >= MODBUS_RTU_MAX_ADU_LENGTH)
        {
            return -1;
        }
        req[currentCount ++] = fileInfo[i].fileNumber >> 8;

        if(currentCount >= MODBUS_RTU_MAX_ADU_LENGTH)
        {
            return -1;
        }
        req[currentCount ++] = fileInfo[i].fileNumber & 0x00ff;

        if(currentCount >= MODBUS_RTU_MAX_ADU_LENGTH)
        {
            return -1;
        }
        req[currentCount ++] = fileInfo[i].recordNumber >> 8;

        if(currentCount >= MODBUS_RTU_MAX_ADU_LENGTH)
        {
            return -1;
        }
        req[currentCount ++] = fileInfo[i].recordNumber & 0x00ff;

        if(currentCount >= MODBUS_RTU_MAX_ADU_LENGTH)
        {
            return -1;
        }
        req[currentCount ++] = fileInfo[i].recordLength >> 8;

        if(currentCount >= MODBUS_RTU_MAX_ADU_LENGTH)
        {
            return -1;
        }
        req[currentCount ++] = fileInfo[i].recordLength & 0x00ff;

        for(j = 0; j < fileInfo[i].recordLength * 2; j++)
        {
            if(currentCount >= MODBUS_RTU_MAX_ADU_LENGTH)
            {
                return -1;
            }
            req[currentCount ++] = fileInfo[i].recordData[j];
        }

    }

    req[2] = currentCount - 3;
    return currentCount;
}

static int _modbus_file_read_request_basis(modbus_t *ctx, int function, modbus_file_io fileInfo[], int infoCount, uint8_t *req)
{
    int  i = 0, j = 0;
    int respCount = 0;

    assert(ctx->slave != -1);
    req[0] = ctx->slave;
    req[1] = function;

    if((infoCount * 7) >= MODBUS_RTU_MAX_ADU_LENGTH)
    {
        return -1;
    }

    j = 3;
    for (; i < infoCount; i++)
    {
        req[j ++] = 6;
        req[j ++] = fileInfo[i].fileNumber >> 8;
        req[j ++] = fileInfo[i].fileNumber & 0x00ff;
        req[j ++] = fileInfo[i].recordNumber >> 8;
        req[j ++] = fileInfo[i].recordNumber & 0x00ff;
        req[j ++] = fileInfo[i].recordLength >> 8;
        req[j ++] = fileInfo[i].recordLength & 0x00ff;
    }
    respCount = infoCount * 7;
    req[2] = respCount;
    return respCount + 3;
}


static int _modbus_file_rtu_over_tcp_build_request_basis(modbus_t *ctx, int function, modbus_file_io fileInfo[], int infoCount, uint8_t *req)
{
    if(function == MODBUS_FC_READ_FILE_RECORD)
    {
        return _modbus_file_read_request_basis(ctx, function, fileInfo, infoCount, req);
    }
    else
    {
       return _modbus_file_write_request_basis(ctx, function, fileInfo, infoCount, req);
    }
}

#endif

static int _modbus_rtu_over_tcp_build_request_basis(modbus_t *ctx, int function,
                                           int addr, int nb,
                                           uint8_t *req)
{
    assert(ctx->slave != -1);
    req[0] = ctx->slave;
    req[1] = function;
    req[2] = addr >> 8;
    req[3] = addr & 0x00ff;
    req[4] = nb >> 8;
    req[5] = nb & 0x00ff;

    return _MODBUS_RTU_PRESET_REQ_LENGTH;
}

static int _modbus_rtu_over_tcp_build_response_basis(sft_t *sft, uint8_t *rsp)
{
    /* Extract from MODBUS Messaging on TCP/IP Implementation
       Guide V1.0b (page 23/46):
       The transaction identifier is used to associate the future
       response with the request. */
    rsp[0] = sft->t_id >> 8;
    rsp[1] = sft->t_id & 0x00ff;

    /* Protocol Modbus */
    rsp[2] = 0;
    rsp[3] = 0;

    /* Length will be set later by send_msg (4 and 5) */

    /* The slave ID is copied from the indication */
    rsp[6] = sft->slave;
    rsp[7] = sft->function;

    return _MODBUS_RTU_PRESET_RSP_LENGTH;
}

const modbus_backend_t _modbus_rtu_over_tcp_backend = {
    _MODBUS_BACKEND_TYPE_TCP,
    _MODBUS_RTU_HEADER_LENGTH,
    _MODBUS_RTU_CHECKSUM_LENGTH,
    MODBUS_TCP_MAX_ADU_LENGTH,
    _modbus_set_slave,
    _modbus_rtu_over_tcp_build_request_basis,
    _modbus_rtu_over_tcp_build_response_basis,
    _modbus_tcp_prepare_response_tid,
    _modbus_rtu_send_msg_pre,
    _modbus_tcp_send,
    _modbus_tcp_receive,
    _modbus_tcp_recv,
    _modbus_tcp_check_integrity,
    _modbus_rtu_pre_check_confirmation,
    _modbus_tcp_connect,
    _modbus_tcp_close,
    _modbus_tcp_flush,
    _modbus_tcp_select,
    _modbus_tcp_free,
#ifdef PDSTARS
	 _modbus_file_rtu_over_tcp_build_request_basis
#endif
};

modbus_t* modbus_new_rtu_over_tcp(const char *ip, int port)
{
    modbus_t *ctx;
    modbus_tcp_t *ctx_tcp;
    size_t dest_size;
    size_t ret_size;

#if defined(OS_BSD)
    /* MSG_NOSIGNAL is unsupported on *BSD so we install an ignore
       handler for SIGPIPE. */
    struct sigaction sa;

    sa.sa_handler = SIG_IGN;
    if (sigaction(SIGPIPE, &sa, NULL) < 0) {
        /* The debug flag can't be set here... */
        fprintf(stderr, "Coud not install SIGPIPE handler.\n");
        return NULL;
    }
#endif

    ctx = (modbus_t *)malloc(sizeof(modbus_t));
    _modbus_init_common(ctx);

    /* Could be changed after to reach a remote serial Modbus device */
    ctx->slave = MODBUS_TCP_SLAVE;

    ctx->backend = &_modbus_rtu_over_tcp_backend;

    ctx->backend_data = (modbus_tcp_t *)malloc(sizeof(modbus_tcp_t));
    ctx_tcp = (modbus_tcp_t *)ctx->backend_data;

    if (ip != NULL) {
        dest_size = sizeof(char) * 16;
        ret_size = strlcpy(ctx_tcp->ip, ip, dest_size);
        if (ret_size == 0) {
            fprintf(stderr, "The IP string is empty\n");
            modbus_free(ctx);
            errno = EINVAL;
            return NULL;
        }

        if (ret_size >= dest_size) {
            fprintf(stderr, "The IP string has been truncated\n");
            modbus_free(ctx);
            errno = EINVAL;
            return NULL;
        }
    } else {
        ctx_tcp->ip[0] = '0';
    }
    ctx_tcp->port = port;
    ctx_tcp->t_id = 0;

    return ctx;
}
