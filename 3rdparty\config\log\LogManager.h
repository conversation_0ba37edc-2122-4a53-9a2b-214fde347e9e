/*
* Copyright (c) 2016.12，南京华乘电气科技有限公司
* All rights reserved.
*
* LogManager.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2016年08月25日
* 修改：邵震宇
* 修改日期：2016年12月5日
* 摘要：日志模块
*    1、允许有多个日志文件实例
*    2、支持的日志文件类型如下：
*      1）循环备份，如xxx.txt.01
*        a、容量到了就开始备份
*        b、条目数到了就开始备份
*      2)按时间备份
*        a、按日期备份
*        b、按月份备份
*        c、按年份备份
*    3、容量限制：
*      日期备份方式，可以设定允许的最大容量，以及达到最大限制后的，回收比例
*    4、支持用<<进行输出
*    5、支持等级控制：Trace、Debug、Info、Warn、Error、Fatal，并且日志输出级别可控制
*    6、支持格式控制可选择：
*      1）等级 + 日期 + 信息
*      2）等级 + 日期 + 路径 + 行号 +　信息
*    7、可选择性使能Debug输出，并且规定日志等级
*    8、可将日志输出到指定的对象（信号、槽方式）
* 当前版本：1.0
*/

#ifndef LOGMANAGER_H
#define LOGMANAGER_H

#include <QString>
#include <QObject>
#include <QtGlobal>
#include <QMutex>
#include "logger_global.h"
#include "logger.h"
#include "LogInfo.h"

#define LOGGER_DEFAULT -1 //默认日志

#define LOG_FILE_LINE //宏开关，默认记录文件名行号

#ifdef LOG_FILE_LINE //如果定义记录
#define LOG_TRACE(iCode) \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::TRACE) {} \
    else  LogManager::instance()->logger(iCode)->trace() << __FILE__ << '@' << __LINE__
#define LOG_DEBUG(iCode) \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::DEBUG) {} \
    else  LogManager::instance()->logger(iCode)->debug() << __FILE__ << '@' << __LINE__
#define LOG_INFO(iCode)  \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::INFO) {} \
    else  LogManager::instance()->logger(iCode)->info() << __FILE__ << '@' << __LINE__
#define LOG_WARN(iCode)  \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::WARN) {} \
    else  LogManager::instance()->logger(iCode)->warn() << __FILE__ << '@' << __LINE__
#define LOG_ERROR(iCode) \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::ERROR) {} \
    else  LogManager::instance()->logger(iCode)->error() << __FILE__ << '@' << __LINE__
#define LOG_FATAL(iCode) \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::FATAL) {} \
    else  LogManager::instance()->logger(iCode)->fatal() << __FILE__ << '@' << __LINE__
#else
#define LOG_TRACE(iCode) \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::TRACE) {} \
    else  LogManager::instance()->logger(iCode)->trace()
#define LOG_DEBUG(iCode) \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::DEBUG) {} \
    else  LogManager::instance()->logger(iCode)->debug()
#define LOG_INFO(iCode)  \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::INFO) {} \
    else  LogManager::instance()->logger(iCode)->info()
#define LOG_WARN(iCode)  \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::WARN) {} \
    else  LogManager::instance()->logger(iCode)->warn()
#define LOG_ERROR(iCode) \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::ERROR) {} \
    else  LogManager::instance()->logger(iCode)->error()
#define LOG_FATAL(iCode) \
    if(LogManager::instance()->logger(iCode)->logLevel() > Logging::FATAL) {} \
    else  LogManager::instance()->logger(iCode)->fatal()
#endif

class LOGGERSHARED_EXPORT LogManager
{
public:
    /************************************************
     * 功能：单例
     ************************************************/
    static LogManager* instance();

    /************************************************
     * 功能：添加日志（循环存储类型）
     * 输入参数：
     *      pInfo -- 日志文件文件配置
     * 返回：
     *      ERROR_NONE -- 成功
     *      其它 -- 失败
     ************************************************/
    Logging::ErrorCode addLogger( Logging::RotateConfigInfo* pInfo );

    /************************************************
     * 功能：添加日志（按时间存储类型）
     * 输入参数：
     *      pInfo -- 日志文件文件配置
     * 返回：
     *      ERROR_NONE -- 成功
     *      其它 -- 失败
     ************************************************/
    Logging::ErrorCode addLogger( Logging::TimeConfigInfo* pInfo );

    /************************************************
     * 功能：获取日志文件句柄
     * 输入参数：
     *      iCode -- 日志文件号
     * 返回：
     *      NULL -- 不存在
     *      其它 -- 配置文件句柄
     ************************************************/
    Logger* logger( int iCode = LOGGER_DEFAULT );
private:
    /************************************************
     * 功能：构造函数
     ************************************************/
    LogManager();

    /****************************
    功能： disable 拷贝
    *****************************/
    LogManager( const LogManager& );

    /****************************
    功能： disable 赋值
    *****************************/
    LogManager & operator = (const LogManager &);
    /************************************************
     * 功能：析构函数
     ************************************************/
    ~LogManager();
private:
    static QMap<int,Logger*> m_mapLoggers;//配置文件
    static QMutex m_mutex;//锁
};

#endif // LOG_H
