#ifndef BOOST_METAPARSE_UTIL_IS_DIGIT_HPP
#define BOOST_METAPARSE_UTIL_IS_DIGIT_HPP

// Copyright <PERSON> (<EMAIL>)  2009 - 2010.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#include <boost/metaparse/v1/util/is_digit.hpp>

namespace boost
{
  namespace metaparse
  {
    namespace util
    {
      using v1::util::is_digit;
    }
  }
}

#endif

