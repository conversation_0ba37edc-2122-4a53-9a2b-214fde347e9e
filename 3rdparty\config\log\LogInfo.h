/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
* LogInfo.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2016年12月05日
* 摘要：日志模块相关信息定义
* 当前版本：1.0
*/
#ifndef LOGINFO_H
#define LOGINFO_H
#include <QtCore>
#include "qslog/QsLogDestFunctor.h"
#include "qslog/QsLogDest.h"

class Logger;
namespace Logging
{
    //循环备份类型
    typedef enum _RotateBackupType
    {
        BACKUP_DISABLE = 0,//不备份
        BACKUP_BY_MAXCOUNT,//根据允许的最大条目数备份
        BACKUP_BY_MAXBYTES,//根据允许的最大容量备份
    }RotateBackupType;

    //时间备份类型
    typedef enum _TimeBackupType
    {
        BACKUP_BY_DAY = 0,//根据日期备份，
        BACKUP_BY_MONTH,//根据月份备份
        BACKUP_BY_YEAR,//根据年备份
    }TimeBackupType;

    const int NO_SPACE_LIMIT = -1;//没有容量限制

    //日志等级
    typedef enum _LogLevel
    {
        TRACE = 0,
        DEBUG,
        INFO,
        WARN,
        ERROR,
        FATAL,
        OFF
    }LogLevel;
    const char TraceString[] = "TRACE";
    const char DebugString[] = "DEBUG";
    const char InfoString[]  = "INFO ";
    const char WarnString[]  = "WARN ";
    const char ErrorString[] = "ERROR";
    const char FatalString[] = "FATAL";
    const QString FILE_FORMAT_BY_DATE = ("yyyy-MM-dd");
    const QString FILE_FORMAT_BY_MONTH = ("yyyy-MM");
    const QString FILE_FORMAT_BY_YEAR = ("yyyy");
    const QString DATETIME_FORMAT = "yyyy-MM-dd hh:mm:ss.zzz";
    inline const char* LevelToText( LogLevel theLevel)
    {
        switch (theLevel)
        {
            case TRACE:
                return TraceString;
            case DEBUG:
                return DebugString;
            case INFO:
                return InfoString;
            case WARN:
                return WarnString;
            case ERROR:
                return ErrorString;
            case FATAL:
                return FatalString;
            case OFF:
                return "";
            default: {
                qWarning("bad log level");
                return InfoString;
            }
        }
    }
    // tries to extract the level from a string log message. If available, conversionSucceeded will
    // contain the conversion result.
    inline LogLevel levelFromMessage(const QString& logMessage, bool* conversionSucceeded)
    {
        if (conversionSucceeded)
            *conversionSucceeded = true;

        if (logMessage.startsWith(QLatin1String(TraceString)))
            return TRACE;
        if (logMessage.startsWith(QLatin1String(DebugString)))
            return DEBUG;
        if (logMessage.startsWith(QLatin1String(InfoString)))
            return INFO;
        if (logMessage.startsWith(QLatin1String(WarnString)))
            return WARN;
        if (logMessage.startsWith(QLatin1String(ErrorString)))
            return ERROR;
        if (logMessage.startsWith(QLatin1String(FatalString)))
            return FATAL;

        if (conversionSucceeded)
            *conversionSucceeded = false;
        return OFF;
    }

    //循环存储日志的配置信息
    typedef struct _RotateConfigInfo
    {
        QString strFileName;//绝对路径文件名
        RotateBackupType eBackupType;//备份类型
        LogLevel eLogLevel;//日志等级

        int iMaxCountOrBytes;//单个文件最大条目数或最大容量
        int iMaxBackupFileCount;//备份的文件个数限制
        int iCode;//日志码（用来唯一区分日志文件）
    }RotateConfigInfo;

    //日志的配置信息
    typedef struct _TimeConfigInfo
    {
        QString strFilePath;//文件路径
        TimeBackupType eBackupType;//备份类型
        LogLevel eLogLevel;//日志等级
        int iMaxTotalFileBytes;//所有文件最大容量限制

        float fRecyclePercent;//达到最大容量时，回收的比例
        QString strSuffix;//文件类型后缀，如".txt",".log"等

        int iCode;//日志码（用来唯一区分日志文件）
    }TimeConfigInfo;

    //错误码
    typedef enum _ErrorCode
    {
        ERROR_NONE = 0,
        LOG_ALREADY_EXIST = -1,//日志已经存在
    }ErrorCode;

    //存储对象列表
    typedef QVector<QsLogging::DestinationPtr> DestinationList;
};

#endif // LOGINFO_H

