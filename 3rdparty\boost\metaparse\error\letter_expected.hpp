#ifndef BOOST_METAPARSE_ERROR_LETTER_EXPECTED_HPP
#define BOOST_METAPARSE_ERROR_LETTER_EXPECTED_HPP

// Copyright <PERSON> (<EMAIL>)  2013.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

#include <boost/metaparse/v1/error/letter_expected.hpp>

namespace boost
{
  namespace metaparse
  {
    namespace error
    {
      using v1::error::letter_expected;
    }
  }
}

#endif

