/*
* Copyright (c) 2016.12，南京华乘电气科技有限公司
* All rights reserved.
*
* Logger.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2016年12月05日
* 修改人：邵震宇
* 摘要：日志文件管理接口*
*    1、支持的日志文件类型如下：
*      1）按后缀备份，如xxx.txt.01
*        a、容量到了就开始备份
*        b、条目数到了就开始备份
*      2)按时间备份
*        a、按月份备份
*        b、按日期备份
*    2、容量限制：
*      1）可限制单个文件的最大容量
*      2）也可限制所有文件的最大容量
*    3、支持用<<进行输出
*    4、支持等级控制：Trace、Debug、Info、Warn、Error、Fatal，并且日志输出级别可控制
*    5、支持格式控制可选择：
*      1）等级 + 日期 + 信息
*      2）等级 + 日期 + 路径 + 行号 +　信息
*    6、可选择性使能Debug输出
*    7、可将日志输出到指定的对象（信号、槽方式）
* 当前版本：1.0
*/

#ifndef LOGGER_H
#define LOGGER_H

#include <QDebug>
#include <QString>
#include "logger_global.h"
#include "LogInfo.h"
#include "LogInstance.h"

class LoggerPrivate;
class LOGGERSHARED_EXPORT Logger
{
public:
    /************************************************
     * 功能：构造函数
     * 输入参数：
     *      pInfo -- 日志文件文件配置
     ************************************************/
    Logger( Logging::RotateConfigInfo* pInfo );

    /************************************************
     * 功能：构造函数
     * 输入参数：
     *      pInfo -- 日志文件文件配置
     ************************************************/
    Logger( Logging::TimeConfigInfo* pInfo );

    /************************************************
     * 功能：析构函数
     ************************************************/
    ~Logger();

    /************************************************
     * 功能：获取记录句柄
     * 输入参数：
     *      eLevel -- 等级
     * 返回：对应的句柄
     ************************************************/
    LogInstance log( Logging::LogLevel eLevel = Logging::DEBUG );

    /************************************************
     * 功能：trace记录
     * 返回：trace句柄引用
     ************************************************/
    LogInstance trace();

    /************************************************
     * 功能：debug记录
     * 返回：debug句柄
     ************************************************/
    LogInstance debug();

    /************************************************
     * 功能：info记录
     * 返回：info句柄
     ************************************************/
    LogInstance info();

    /************************************************
     * 功能：warn记录
     * 返回：warn句柄
     ************************************************/
    LogInstance warn();

    /************************************************
     * 功能：error记录
     * 返回：error句柄
     ************************************************/
    LogInstance error();

    /************************************************
     * 功能：fatal记录
     * 返回：fatal句柄
     ************************************************/
    LogInstance fatal();

    /************************************************
     * 功能：获取日志等级
     * 返回：日志等级
     ************************************************/
    Logging::LogLevel logLevel() const;

    /************************************************
     * 功能：设置日志等级
     * 输入参数：
     *       eLogLevel -- 日志等级
     ************************************************/
    void setLogLevel( Logging::LogLevel eLogLevel );

    /************************************************
     * 功能：使能调试输出
     * 输入参数：
     *       eLogLevel -- 调试输出的日志等级
     ************************************************/
    void enableDebugOutput( Logging::LogLevel eLogLevel );

    /*************************************************
    功能： 将日志输出到接收日志的自定义对象
    输入参数： receiver：接收日志消息对象指针
              member：响应日志消息槽函数，槽函数参数格式为(QString, int)：QString - 日志内容 int - 日志级别
    *************************************************************/
    void addReceiver(QObject *receiver, const char *member);
private:
    Logger(const Logger&);            // not available
    Logger& operator=(const Logger&); // not available
private:
    LoggerPrivate* d;
};

#endif // QSLOG_H
