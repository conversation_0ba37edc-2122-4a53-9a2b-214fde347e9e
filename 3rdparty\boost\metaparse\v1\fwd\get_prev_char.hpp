#ifndef BOOST_METAPARSE_V1_FWD_GET_PREV_CHAR_HPP
#define BOOST_METAPARSE_V1_FWD_GET_PREV_CHAR_HPP

//    Copyright <PERSON> (<EMAIL>) 2013.
// Distributed under the Boost Software License, Version 1.0.
//    (See accompanying file LICENSE_1_0.txt or copy at
//          http://www.boost.org/LICENSE_1_0.txt)

namespace boost
{
  namespace metaparse
  {
    namespace v1
    {
      template <class>
      struct get_prev_char_impl;

      template <class>
      struct get_prev_char;
    }
  }
}

#endif

