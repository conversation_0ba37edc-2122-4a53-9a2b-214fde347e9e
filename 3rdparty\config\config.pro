#-------------------------------------------------
#
# Project created by QtCreator 2017-04-21T13:20:15
#
#-------------------------------------------------

QT       += xml

QT       -= gui

TEMPLATE = lib

DEFINES += CONFIG_LIBRARY

win32 {
    CONFIG(debug, debug|release){
        LIBS += -L$$PWD -llog_d_541
        TARGET = config_d_541
    }else{
        LIBS += -L$$PWD -llog_r_541
        TARGET = config_r_541
    }

}

unix {
    DEFINES += ARM
    if(contains(DEFINES,ARM)){
        LIBS += -L$$PWD/lib/arm -llog_r_541
    }else{
        LIBS += -L$$PWD/lib/linux -llog_d_541
    }
}



SOURCES += \
    xmlsetting/XmlSettings.cpp \
    ConfigInstance.cpp \
    ConfigManager.cpp

HEADERS +=\
    xmlsetting/XmlSettings.h \
    ConfigInfo.h \
    ConfigInstance.h \
    ConfigManager.h \

